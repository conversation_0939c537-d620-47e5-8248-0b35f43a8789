<script>
    import { H1, P1, P2, <PERSON><PERSON> } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    import SignUpButton from '$lib/landingPage/SignUpButton.svelte';

    import heroImage from '$lib/assets/home/<USER>';
    
    let { 
        headline = "You don't need to burn out to get a good score",
        subheadline = "The only gamify SAT platform that help you beat the test while enjoying the process.",
        features = [
            "See clear improvements over time",
            "Keep you away from feeling overwhelmed and demotivated", 
            "Get you addicted to learning instead of hating it"
        ],
        primaryCTA = "Get Addicted",
        secondaryCTA = "Explore More",
        riskReversal = "Money back guarantee <b>(it's free)</b>"
    } = $props();
</script>

<SectionWrapper --bg-color="var(--rose)" --padding-top="4rem" --padding-bottom="4rem">
<div class="hero-container">
    <div class="hero-content">
        <H1 --text-align="center">{@html headline}</H1>
        <P1>{@html subheadline}</P1>
        
        <div class="hero-features">
            {#each features as feature}
                <div class="feature-item">
                    <div class="check-icon">✓</div>
                    <P2>{feature}</P2>
                </div>
            {/each}
        </div>
        
        <div class="hero-buttons">
            <SignUpButton>{@html primaryCTA}</SignUpButton>
            <!-- <Button isSecondary>{@html secondaryCTA}</Button> -->
        </div>

        <!-- <div class="risk-reversal">
            <div class="check-icon">✓</div>
            <P2>{@html riskReversal}</P2>
        </div> -->
    </div>
    
    <div class="hero-image">
        <enhanced:img 
            src={heroImage} 
            alt="DSAT Platform Preview"
            class="hero-img"
        />
    </div>
</div>
</SectionWrapper>

<style>
    /* Hero */
    .hero-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: center;
        width: 100%;
        max-width: 90rem;
        margin: 0 auto;
    }
    
    .hero-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        text-align: left;
    }
    
    .hero-features {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .feature-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .check-icon {
        background: var(--pitch-black);
        color: white;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.2rem;
        border-radius: 0.25rem;
        flex-shrink: 0;
    }
    
    .hero-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .risk-reversal {
        display: flex;
        align-items: center;
        gap: 1rem;
        /* justify-content: center; */
        opacity: 0.8;
    }
    
    .hero-image {
        display: flex;
        justify-content: center;
        background-color: var(--white);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black); 
        overflow: hidden;
   }
    
    .hero-img {
        width: 100%;
        aspect-ratio: 1.72;
        object-fit: contain;
        object-position: center;
    }

    /* Mobile Responsiveness */
    @media (max-width: 1200px) {
        .hero-container {
            grid-template-columns: 1fr;
            gap: 2rem;
            padding: 0 1rem;
        }
        
        .hero-content {
            text-align: center;
            gap: 1.5rem;
        }
        
        .hero-features {
            gap: 0.75rem;
            margin: 0 auto;
        }
        
        .feature-item {
            gap: 0.75rem;
            text-align: start;
        }
        
        .check-icon {
            width: 1.75rem;
            height: 1.75rem;
            font-size: 1rem;
            border-radius: 0.25rem;
            flex-shrink: 0;
        }
        
        .hero-img {
            height: 18rem;
        }

        .hero-buttons {
            margin: 0 auto;
        }
    }

    /* Large Mobile Responsiveness */
    @media (max-width: 768px) {
        .hero-container {
            gap: 1.5rem;
            padding: 0 0.5rem;
        }
        
        .hero-content {
            gap: 1.25rem;
        }
    }

    /* Small Mobile Responsiveness */
    @media (max-width: 540px) {
        .hero-container {
            gap: 1rem;
        }
        
        .hero-content {
            gap: 1rem;
        }
        
        .hero-features {
            gap: 0.5rem;
        }
        
        .check-icon {
            width: 1.5rem;
            height: 1.5rem;
            font-size: 0.875rem;
            flex-shrink: 0;
        }
        
        .hero-img {
            height: 12rem;
        }
    }
</style>
