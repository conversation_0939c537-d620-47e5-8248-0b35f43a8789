// Mission system types
export interface Mission {
	id: string;
	name: string;
	description: string;
	metric: MissionMetric;
	target: number;
	period: MissionPeriod;
	reward?: number;
	// UI metadata
	route?: string;
	buttonText?: string;
}

export interface DailyMissionProgress {
	missions: Record<string, number>; // missionId -> current progress
	completed: boolean;
	completedAt?: Date;
	createdAt: Date;
}

export interface UserStreakData {
	currentStreak: number;
	bestStreak: number;
	lastMissionDate?: string; // YYYY-MM-DD format
}

export enum MissionMetric {
	QUESTIONS_ANSWERED = 'questions_answered',
	VOCAB_SESSION = 'vocab_session',
	MORNING_LOGIN = 'morning_login',
	EVENING_LOGIN = 'evening_login'
}

export enum MissionPeriod {
	DAILY = 'daily',
	WEEKLY = 'weekly'
}
