<script>
    import { H2, P1, P2, But<PERSON> } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    import SignUpButton from '$lib/landingPage/SignUpButton.svelte';
    
    let { 
        headline = "Switch to DSAT16 in 5 minutes or less",
        subheadline = "You shouldn't have to sacrifice all your happiness to study. With DSAT16, you can finally get in your dream university without burning out.",
        features = [
            "14-day free trial",
            "No credit card required",
            "Cancel any time, no question asked"
        ],
        primaryCTA = "Start Working Smarter, Not Harder",
        secondaryCTA = "{Secondary CTA}",
        socialProof = "{Social Proof goes here}",
        imagePlaceholder = "CTA IMAGE"
    } = $props();
</script>

<SectionWrapper --bg-color="var(--rose)" --padding-top="8rem" --padding-bottom="8rem">
<div class="cta-section">
    <div class="cta-content">
        <H2>{@html headline}</H2>
        <P1>{@html subheadline}</P1>

        <div class="cta-features">
            {#each features as feature}
                <div class="feature-item">
                    <div class="check-icon">✓</div>
                    <P2>{feature}</P2>
                </div>
            {/each}
        </div>

        <div class="cta-buttons">
            <SignUpButton>{@html primaryCTA}</SignUpButton>
            <!-- <Button isSecondary>{@html secondaryCTA}</Button> -->
        </div>
        
        <!-- <div class="social-proof-item">
            <div class="check-icon">✓</div>
            <P2>{@html socialProof}</P2>
        </div> -->
    </div>
    
    <div class="cta-image">
        <div class="placeholder-image">
            <P2>{imagePlaceholder}</P2>
        </div>
    </div>
</div>
</SectionWrapper>

<style>
    /* CTA Section */
    .cta-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: center;
                width: 100%;
    }
    
    .cta-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }
    
    .cta-features {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .feature-item {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .check-icon {
        background: var(--pitch-black);
        color: white;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.2rem;
        flex-shrink: 0;
    }
    
    .cta-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .social-proof-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border: 0.1875rem solid var(--pitch-black);
        background: white;
    }
    
    .cta-image {
        display: flex;
        justify-content: center;
    }
    
    .placeholder-image {
        width: 100%;
        height: 25rem;
        background: var(--pitch-black);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 0.25rem solid var(--pitch-black);
        font-weight: 700;
    }
    
    /* Mobile Responsiveness */
    @media (max-width: 48rem) {
        .cta-section {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .placeholder-image {
            height: 15.625rem;
        }
    }
</style>
