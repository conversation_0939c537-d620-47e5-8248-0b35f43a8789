import { writable, derived, type Writable } from 'svelte/store';
import { onSnapshot, doc, type Unsubscribe } from 'firebase/firestore';
import { db } from '../firebase/firestore.js';
import { user } from '../firebase/auth.svelte.js';
import type { DailyMissionProgress, UserStreakData } from '../types/mission.types.js';
import { getTodayKey, initializeDailyProgress, migrateMissionProgress } from './missionEngine.js';
import { getDailyMissions } from './missionCatalog.js';

// Store for today's mission progress
export const missionProgress: Writable<DailyMissionProgress | null> = writable(null);

// Store for user streak data
export const streakData: Writable<UserStreakData | null> = writable(null);

// Loading and error states
export const missionLoading = writable(true);
export const missionError = writable<string | null>(null);

// Derived store for mission completion percentages
export const missionCompletionPercentages = derived(
	missionProgress,
	($progress) => {
		if (!$progress) return {};
		
		const dailyMissions = getDailyMissions();
		const percentages: Record<string, number> = {};
		
		dailyMissions.forEach(mission => {
			const current = $progress.missions[mission.id] || 0;
			percentages[mission.id] = Math.min((current / mission.target) * 100, 100);
		});
		
		return percentages;
	}
);

// Derived store for overall completion status
export const allMissionsComplete = derived(
	missionProgress,
	($progress) => {
		if (!$progress) return false;

		// Get current daily missions from catalog
		const dailyMissions = getDailyMissions();

		// Check if all missions are actually complete
		return dailyMissions.every(mission => {
			const currentProgress = $progress.missions[mission.id] || 0;
			return currentProgress >= mission.target;
		});
	}
);

let progressUnsubscribe: Unsubscribe | null = null;
let streakUnsubscribe: Unsubscribe | null = null;

/**
 * Subscribe to mission progress for the current user
 */
export function subscribeMissionProgress(userId: string): void {
	// Clean up existing subscription
	if (progressUnsubscribe) {
		progressUnsubscribe();
	}

	missionLoading.set(true);
	missionError.set(null);

	const todayKey = getTodayKey();
	const progressRef = doc(db, 'users', userId, 'missionProgress', todayKey);

	progressUnsubscribe = onSnapshot(
		progressRef,
		async (doc) => {
			try {
				let data: DailyMissionProgress;

				if (doc.exists()) {
					data = doc.data() as DailyMissionProgress;
					// Migrate existing progress to include new missions
					data = await migrateMissionProgress(userId, data);
				} else {
					// Initialize progress for today if it doesn't exist
					data = await initializeDailyProgress(userId);
				}

				missionProgress.set(data);
				missionLoading.set(false);
			} catch (error) {
				console.error('Error loading mission progress:', error);
				missionError.set('Failed to load mission progress');
				missionLoading.set(false);
			}
		},
		(error) => {
			console.error('Error subscribing to mission progress:', error);
			missionError.set('Failed to subscribe to mission progress');
			missionLoading.set(false);
		}
	);
}

/**
 * Subscribe to streak data for the current user
 */
export function subscribeStreakData(userId: string): void {
	// Clean up existing subscription
	if (streakUnsubscribe) {
		streakUnsubscribe();
	}

	const userRef = doc(db, 'users', userId);

	streakUnsubscribe = onSnapshot(
		userRef,
		(doc) => {
			try {
				if (doc.exists()) {
					const userData = doc.data();
					const userStreakData: UserStreakData = userData?.streakData || {
						currentStreak: 0,
						bestStreak: 0
					};
					streakData.set(userStreakData);
				} else {
					streakData.set({
						currentStreak: 0,
						bestStreak: 0
					});
				}
			} catch (error) {
				console.error('Error loading streak data:', error);
			}
		},
		(error) => {
			console.error('Error subscribing to streak data:', error);
		}
	);
}

/**
 * Initialize mission subscriptions when user changes
 */
user.subscribe(($user) => {
	if ($user?.uid) {
		subscribeMissionProgress($user.uid);
		subscribeStreakData($user.uid);
	} else {
		// Clean up subscriptions when user logs out
		if (progressUnsubscribe) {
			progressUnsubscribe();
			progressUnsubscribe = null;
		}
		if (streakUnsubscribe) {
			streakUnsubscribe();
			streakUnsubscribe = null;
		}
		
		// Reset stores
		missionProgress.set(null);
		streakData.set(null);
		missionLoading.set(true);
		missionError.set(null);
	}
});

/**
 * Cleanup function for component destruction
 */
export function cleanupMissionSubscriptions(): void {
	if (progressUnsubscribe) {
		progressUnsubscribe();
		progressUnsubscribe = null;
	}
	if (streakUnsubscribe) {
		streakUnsubscribe();
		streakUnsubscribe = null;
	}
}
