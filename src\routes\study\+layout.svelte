<script lang="ts">
    import { page } from "$app/state";
	import { checkLoginMissions } from "$lib/missions/missionEngine.js";
    import NavBar from "$lib/study/NavBar.svelte";
	import { innerWidth } from "svelte/reactivity/window";

    let { children, data } = $props();

    let currentPath = $derived(page.url.pathname); 

    // Check login missions when user visits the dashboard
    $effect(() => {
        data.uid && checkLoginMissions(data.uid);
    });
</script>

<NavBar {currentPath} role={data.role}/>

<div class="children-container" class:mobile={innerWidth.current < 1024}>
    {@render children()}
</div>

<style>
    .children-container {
        margin: 0 auto;
        margin-left: 4.75rem;
    }

    .mobile {
        margin-left: 0;
        margin-top: 4rem; /* Account for fixed mobile top navbar */
    }
</style>

