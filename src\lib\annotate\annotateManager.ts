import { writable, get } from "svelte/store";

export let selectedRange = writable([null, null]);
export let selectedName = writable(null);

export function annotateManager(rootElement: HTMLElement, name: string) {
    const positions = [];
    const marks = new Map();
    const marksToHighlights = new Map();
    const highlightColors = new Map();
    const markColors = new Map();
    const highlightToFloatingDiv = new Map();
    let root: HTMLElement = rootElement;

    const colors =  {
        yellow: {
            normal: "#fff9c4",
            hover: "yellow"
        },
        red: {
            normal: "#ffcccc",
            hover: "red"
        },
        selected: {
            normal: 'lightblue',
            hover: "blue"
        }
    }

    // Get the total offset of a target node and offset within the root node
    function getTotalOffset(targetNode, targetOffset) {
        const iterator = document.createNodeIterator(root, NodeFilter.SHOW_TEXT);
        let currentNode;
        let totalOffset = 0;
        while ((currentNode = iterator.nextNode())) {
            if (currentNode === targetNode) {
                return totalOffset + targetOffset;
            }
            totalOffset += currentNode.textContent.length;
        }
        return -1; // Not found
    }


    // Get the range of a given start and end position within the root node
    function getRange(startPos, endPos) {
        let charCount = 0;
        let startNode, startNodeOffset;
        let endNode, endNodeOffset;
    
        const iterator = document.createNodeIterator(root, NodeFilter.SHOW_TEXT);
    
        let currentNode;
        while ((currentNode = iterator.nextNode())) {
            const nextCharCount = charCount + currentNode.textContent.length;
            if (!startNode && startPos >= charCount && startPos <= nextCharCount) {
                startNode = currentNode;
                startNodeOffset = startPos - charCount;
            }
            if (!endNode && endPos >= charCount && endPos <= nextCharCount) {
                endNode = currentNode;
                endNodeOffset = endPos - charCount;
                break;
            }
            charCount = nextCharCount;
        }
    
        if (!startNode || !endNode) {
            console.warn("Offsets out of range");
            return;
        }
    
        const range = document.createRange();
        range.setStart(startNode, startNodeOffset);
        range.setEnd(endNode, endNodeOffset);

        return range;
    }
    
    
    // Create a mark element with the given start and end offsets within the root node
    function createMark(startPos, endPos, wrapperTag = "mark", focus = false) {
        const range = getRange(startPos, endPos);
        const extracted = range.extractContents();
        const mark = document.createElement(wrapperTag);
        mark.appendChild(extracted);
        marks.set(`${startPos},${endPos}`, mark);
        range.insertNode(mark);
        const highlightList = marksToHighlights.get(`${startPos},${endPos}`);
        const minHighlight = highlightList.reduce((min: [number, number], item: [number, number]) => min[1] - min[0] < item[1] - item[0] ? min : item, highlightList[0]);
        const color = highlightColors.get(`${minHighlight[0]},${minHighlight[1]}`);
        
        if (!focus) {
            mark.style.backgroundColor = colors[color].normal;
            addHoverEffect(startPos, endPos);
            addFocusEvent(startPos, endPos);
        } else {
            // Get the floating div
            const floatingDiv = highlightToFloatingDiv.get(`${minHighlight[0]},${minHighlight[1]}`);
            floatingDiv.style.display = "none";
            mark.style.backgroundColor = colors[color].hover;
            addNonFocusEvent(startPos, endPos);
            mark.focus();
        }        
        markColors.set(`${startPos},${endPos}`, color);
    }
    
    
    // Remove a mark element with the given start and end offsets within the root node
    function removeMark(startOffset, endOffset) {
        const mark = marks.get(`${startOffset},${endOffset}`);
        marks.delete(`${startOffset},${endOffset}`);
        marksToHighlights.delete(`${startOffset},${endOffset}`);
        if (!mark) return;
        const parent = mark.parentNode;
        while (mark.firstChild) {
            parent.insertBefore(mark.firstChild, mark);
        }
        parent.removeChild(mark);
    }


    // Split the mark element into two marks at the given position
    function splitMarks(startPos, endPos, position) {
        let array = marksToHighlights.get(`${startPos},${endPos}`);
        marksToHighlights.set(`${startPos},${position}`, [...array]);
        marksToHighlights.set(`${position},${endPos}`, [...array]);
        removeMark(startPos, endPos);
        createMark(startPos, position);
        createMark(position, endPos);
    }


    // Merge two mark elements into one
    function mergeMarks(startOffset, endOffset, position) {
        let array = marksToHighlights.get(`${startOffset},${position}`);
        marksToHighlights.set(`${startOffset},${endOffset}`, [...array]);
        removeMark(startOffset, position);
        removeMark(position, endOffset);
        createMark(startOffset, endOffset);
    }


    // Create floating div
    function createFloatingDiv(startPos, endPos, textContent) {

        const floatingStyles = {
            position: 'absolute',
            // bottom: '100%', // place above the span
            // left: '0',      // align with left edge (start)
            // transform: 'translateY(-8px)', // small gap above
            
            display: 'inline-flex',
            minWidth: '100px',
            padding: '5px',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '10px',
            borderRadius: '10px',
            border: '2px solid #000',
            background: '#FFF',
            color: '#000', 
            fontFamily: 'Inter',
            fontSize: '17px',
            fontStyle: 'normal',
            fontWeight: '500',
            lineHeight: 'normal' 
        };

        // Create a floating div
        const floatingDiv = document.createElement("div");
        Object.assign(floatingDiv.style, floatingStyles);
        floatingDiv.textContent = textContent;

        const range = getRange(startPos, endPos);
        const rect = range.getBoundingClientRect();
        floatingDiv.style.display = "block"; 
        document.body.appendChild(floatingDiv);
        const divWidth = floatingDiv.offsetWidth;
        const divHeight = floatingDiv.offsetHeight;
        floatingDiv.style.display = "none"; // hide initially

        floatingDiv.style.left = `${rect.left + rect.width / 2 - divWidth / 2 + window.scrollX}px`;
        floatingDiv.style.top = `${rect.top - divHeight - 20 + window.scrollY}px`; // 8px above the target

        highlightToFloatingDiv.set(`${startPos},${endPos}`, floatingDiv);
    }


    // Add a hover effect to the mark element
    function addHoverEffect(startPos, endPos) {
        const markKey = `${startPos},${endPos}`;
        const mark = marks.get(markKey);
        const highlightList = marksToHighlights.get(markKey);
        if (!mark || !highlightList) return;

        // Get the smallest highlight
        const minHighlight = highlightList.reduce((min: [number, number], item: [number, number]) => min[1] - min[0] < item[1] - item[0] ? min : item, highlightList[0]);
        const highlightKey = `${minHighlight[0]},${minHighlight[1]}`;
        const color = highlightColors.get(highlightKey);
        const floatingDiv = highlightToFloatingDiv.get(highlightKey);

        
        // Create new handlers
        const enter = () => {
            for (let i = 0; i < positions.length - 1; i++) {
                if (positions[i] >= minHighlight[0] && positions[i + 1] <= minHighlight[1]) {
                    const m = marks.get(`${positions[i]},${positions[i + 1]}`);
                    if (m) m.style.backgroundColor = colors[color].hover;
                }
            }
            if (floatingDiv.textContent !== "") {
                const range = getRange(minHighlight[0], minHighlight[1]);
                const rect = range.getBoundingClientRect();
                floatingDiv.style.display = "block";
                floatingDiv.style.left = `${rect.left + rect.width / 2 - floatingDiv.offsetWidth / 2 + window.scrollX}px`;
                floatingDiv.style.top = `${rect.top - floatingDiv.offsetHeight - 20 + window.scrollY}px`;
            }
        };

        const leave = () => {
            for (let i = 0; i < positions.length - 1; i++) {
                if (positions[i] >= minHighlight[0] && positions[i + 1] <= minHighlight[1]) {
                    const m = marks.get(`${positions[i]},${positions[i + 1]}`);
                    if (m) m.style.backgroundColor = colors[markColors.get(`${positions[i]},${positions[i + 1]}`)].normal;
                }
            }
            floatingDiv.style.display = "none";
        };

        // Add new listeners
        mark.addEventListener("mouseenter", enter);
        mark.addEventListener("mouseleave", leave);
    }


    // Add a click event to the mark element
    function addFocusEvent(startOffset, endOffset) {
        const mark = marks.get(`${startOffset},${endOffset}`);
        const highlightList = marksToHighlights.get(`${startOffset},${endOffset}`);
        const minHighlight = highlightList.reduce((min, item) => min[1] - min[0] < item[1] - item[0] ? min : item, highlightList[0]);
        
        mark.addEventListener("click", () => {
            setSelected(minHighlight[0], minHighlight[1]);
        });
    }


    function addNonFocusEvent(startOffset, endOffset) {
        // const mark = marks.get(`${startOffset},${endOffset}`);
        // const highlightList = marksToHighlights.get(`${startOffset},${endOffset}`);
        // const minHighlight = highlightList.reduce((min, item) => min[1] - min[0] < item[1] - item[0] ? min : item, highlightList[0]);
        // mark.tabIndex = 0;

        // mark.addEventListener("blur", () => {
        //     setSelected(null, null);
        // });
    }
    
    

    // Add a highlight to the mark element
    function addHighlightToMark(markStartPos, markEndPos, highlightStartPos, highlightEndPos) {
        const key = `${markStartPos},${markEndPos}`;
        if (!marksToHighlights.has(key)) {
            marksToHighlights.set(key, []);
        }
        marksToHighlights.get(key).push([highlightStartPos, highlightEndPos]);
    }


    // Update the mark element with the given start and end offsets within the root node
    function updateMarks(startPos, endPos, focus = false) {
        if (marks.has(`${startPos},${endPos}`)) {
            const tempHighlights = [...(marksToHighlights.get(`${startPos},${endPos}`) || [])];
            removeMark(startPos, endPos);
            marksToHighlights.set(`${startPos},${endPos}`, tempHighlights);
        }
        createMark(startPos, endPos, "mark", focus);
    }


    // Add highlight
    function addHighlights(startPos, endPos) {
        let startIndex = positions.findIndex(x => x >= startPos);
        let newStartPos = true;

        if (startIndex === -1) {
            positions.push(startPos);
            startIndex = positions.length-1;
        } else if (positions[startIndex] !== startPos) {
            positions.splice(startIndex, 0, startPos);
        } else {
            newStartPos = false;
        }

        if (newStartPos && startIndex > 0 && startIndex < positions.length - 1 && marks.has(`${positions[startIndex - 1]},${positions[startIndex + 1]}`)) {
            splitMarks(positions[startIndex - 1], positions[startIndex + 1], startPos);
        }

        let endIndex = positions.findIndex(x => x >= endPos);
        let newEndPos = true;

        if (endIndex === -1) {
            positions.push(endPos);
            endIndex = positions.length - 1;
        } else if (positions[endIndex] !== endPos) {
            positions.splice(endIndex, 0, endPos);
        } else {
            newEndPos = false;
        }

        if (newEndPos && endIndex < positions.length - 1 && marks.has(`${positions[endIndex - 1]},${positions[endIndex + 1]}`)) {
            splitMarks(positions[endIndex - 1], positions[endIndex + 1], endPos);
        }

        for (let i = startIndex; i < endIndex; i++) {
            addHighlightToMark(positions[i], positions[i + 1], startPos, endPos);
        }

        setSelected(startPos, endPos);
        normalizeDeep(root);
    }


    // Normalize the positions array and the marks within the root node
    function normalizeMarks(startIndex, endIndex) {

        // Remove marks that have no highlights
        for (let i = startIndex; i < endIndex; i++) {
            const key = `${positions[i]},${positions[i + 1]}`;
            const highlightsList = marksToHighlights.get(key);
            if (highlightsList.length === 0) {
                removeMark(positions[i], positions[i + 1]);
            } else {
                updateMarks(positions[i], positions[i + 1]);
            }
        }

        // Remove positions that are not in use
        for (let i = startIndex; i <= endIndex; i++) {
            if (startIndex === endIndex) {
                positions.splice(i, 1);
            }

            const prev = positions[i - 1];
            const curr = positions[i];
            const next = positions[i + 1];
        
            const hasPrev = prev !== undefined && marks.has(`${prev},${curr}`);
            const hasNext = next !== undefined && marks.has(`${curr},${next}`);
        
            if (!hasPrev && !hasNext) {
                positions.splice(i, 1);
                i--;
                endIndex--;
            }
        }
        
        // Merge marks that belong to same highlights
        let prev = null;
        for (let i = startIndex - 1; i <= endIndex; i++) {
            const cur = positions[i];
            const next = positions[i + 1];
            if (cur === undefined || next === undefined || !marks.has(`${cur},${next}`)) {
                prev = null;
                continue;
            }

            if (prev === null) {
                prev = cur;
                continue;
            }

            const keyPrev = `${prev},${cur}`;
            const keyCurr = `${cur},${next}`;
            const highlightsList1 = marksToHighlights.get(keyPrev);
            const highlightsList2 = marksToHighlights.get(keyCurr);
            const isSame = highlightsList1.length === highlightsList2.length && highlightsList1.every(item => highlightsList2.some(item2 => item[0] === item2[0] && item[1] === item2[1]));
            if (isSame) {
                mergeMarks(prev, next, cur);
                positions.splice(i, 1);
                i--;
                endIndex--;
            } else {
                prev = cur;
            }
        }

        normalizeDeep(root);
    }
    

    // Normalize the DOM tree
    function normalizeDeep(root) {
        let prev = null;

        let i = 0;
        while (i < root.childNodes.length) {
            const node = root.childNodes[i];
            if (node instanceof HTMLElement) {
                // Recursively normalize children first
                normalizeDeep(node);

                // Remove empty elements
                if (node.childNodes.length === 0) {
                    root.removeChild(node);
                    continue;
                }

                // Merge adjacent elements with same tag and no attributes
                if (prev &&
                    prev instanceof HTMLElement &&
                    prev.tagName === node.tagName &&
                    prev.attributes.length === 0 &&
                    node.attributes.length === 0
                ) {
                    while (node.firstChild) {
                        prev.appendChild(node.firstChild);
                    }
                    root.removeChild(node);
                    continue;
                }
            }

            // Save this node as previous
            prev = node;
            i++;
        }

        root.normalize();
    }

    // Remove a highlight from the mark element
    function removeHighlight(startPos, endPos) {
        let startIndex = positions.findIndex(x => x === startPos);
        let endIndex = positions.findIndex(x => x === endPos);
        highlightColors.delete(`${startPos},${endPos}`);
        const floatingDiv = highlightToFloatingDiv.get(`${startPos},${endPos}`);
        floatingDiv?.remove();
        highlightToFloatingDiv.delete(`${startPos},${endPos}`);

        for (let i = startIndex; i < endIndex; i++) {
            const key = `${positions[i]},${positions[i + 1]}`;
            let highlightsList = marksToHighlights.get(key);
            highlightsList = highlightsList.filter(item => item[0] !== startPos || item[1] !== endPos);
            marksToHighlights.set(key, highlightsList);
        }
        normalizeMarks(startIndex, endIndex);
    }


    // Highlight the selected text
    function highlight(color = "yellow") {
        // Get the selection
        if (root === null) return false;
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);

            // Get the total offset of the start and end of the range
            let startPos = getTotalOffset(range.startContainer, range.startOffset);
            let endPos = getTotalOffset(range.endContainer, range.endOffset);
            
            if (startPos === -1 || endPos === -1 || startPos === endPos) {
                return false;
            }

            highlightColors.set(`${startPos},${endPos}`, color);
            createFloatingDiv(startPos, endPos, "");
            addHighlights(startPos, endPos);

            // Collapse the range to the end
            range.collapse(false);
            return true;
        }
        return false;
    }


    function getText(startPos, endPos) {
        const range = getRange(startPos, endPos);
        return range.toString();
    }

    // Update highlights for effects
    function updateNonSelected(startPos, endPos) {
        let startIndex = positions.findIndex(x => x >= startPos);
        let endIndex = positions.findIndex(x => x >= endPos);
        for (let i = startIndex; i < endIndex; i++) {
            updateMarks(positions[i], positions[i + 1]);
        }
    }

    function updateSelected(startPos, endPos) {
        let startIndex = positions.findIndex(x => x >= startPos);
        let endIndex = positions.findIndex(x => x >= endPos);
        for (let i = startIndex; i < endIndex; i++) {
            updateMarks(positions[i], positions[i + 1], true);
        }
    }


    function setSelected(startPos, endPos) {
        
        let selected = get(selectedRange);

        // If there exist current selection
        if(selected[0]) {
            updateNonSelected(selected[0], selected[1]);
        }

        selectedRange.set([startPos, endPos]);
        selectedName.set(name);

        selected = get(selectedRange);
        if (selected[0]) {
            updateSelected(selected[0], selected[1]);
        }
    }

    function getAnnoText() {
        const selected = get(selectedRange);
        if (selected[0]) {
            const floatingDiv = highlightToFloatingDiv.get(`${selected[0]},${selected[1]}`);
            return floatingDiv.textContent;
        }
    }

    function setAnnoText(text: string) {
        const selected = get(selectedRange);
        const floatingDiv = highlightToFloatingDiv.get(`${selected[0]},${selected[1]}`);
        floatingDiv.textContent = text;
    }


    function getSelectedText() {
        const selected = get(selectedRange);
        return getText(selected[0], selected[1]);
    }

    function deleteHighlight() {
        const selected = get(selectedRange);
        removeHighlight(selected[0], selected[1]);
        selectedRange.set([null, null]);
    }


    function reRenderHighlights() {
        positions.forEach((pos, i) => {
            if (i === positions.length - 1) return;
            const nextPos = positions[i + 1];
            if (marks.has(`${pos},${nextPos}`)) createMark(pos, nextPos);
        });
    }
    
    function setRoot(rootElement: HTMLElement) {
        root = rootElement;
        reRenderHighlights();
    }


    function destroy() {
        positions.length = 0;
        marks.clear();
        marksToHighlights.clear();
        highlightColors.clear();
        markColors.clear();
        for (const floatingDiv of highlightToFloatingDiv.values()) {
            floatingDiv.remove();
        }
        highlightToFloatingDiv.clear();
        root = null;
    }


    return {
        highlight,
        setSelected,
        getAnnoText,
        setAnnoText,
        getSelectedText,
        deleteHighlight, 
        setRoot,
        destroy
    }
}
