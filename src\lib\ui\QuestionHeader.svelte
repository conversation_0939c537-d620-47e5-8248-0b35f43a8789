<script lang="ts">
	/**
	 * Question Header Component
	 * Displays timer, annotation tools, and chat functionality
	 */
	import H4 from "./typography/H4.svelte";
	import { onMount } from "svelte";

	let { onAnnotateClick, annoButton = $bindable(null), questionIndex = 0, isAnswered = false, onBackClick } = $props();

	// Timer state
	let seconds = $state(0);
	let minutes = $state(0);

	// Format time as MM:SS
	let formattedTime = $derived(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);

	// Example timer functionality
	let timerInterval = null;

	function startTimer() {
		if (timerInterval) return;

		timerInterval = setInterval(() => {
			seconds++;
			if (seconds >= 60) {
				seconds = 0;
				minutes++;
			}
		}, 1000);
	}

	function stopTimer() {
		if (timerInterval) {
			clearInterval(timerInterval);
			timerInterval = null;
		}
	}

	function resetTimer() {
		stopTimer();
		seconds = 0;
		minutes = 0;
	}

	// Reset and restart timer when question index changes
	$effect(() => {
		// This effect runs whenever questionIndex changes
		questionIndex;
		resetTimer();
		startTimer();
	});

	// Pause/resume timer based on answer state
	$effect(() => {
		if (isAnswered) {
			stopTimer();
		} else {
			// Only start if we have a question (questionIndex >= 0) and timer isn't already running
			if (questionIndex >= 0 && !timerInterval) {
				startTimer();
			}
		}
	});

	onMount(() => {
		startTimer();
	});
</script>

<div class="question-header">
	<div class="header-content">
		<!-- Left spacer -->
		<!-- <div class="spacer"> -->
			{@render backButton()}
		<!-- </div> -->
		
		<!-- Timer (center) -->
		<H4>{formattedTime}</H4>
		
		<!-- Tools (right) -->
		<div class="tools">
			<button class="tool-button" onclick={onAnnotateClick} bind:this={annoButton} aria-label="Annotation tool">
				<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M22 7.74002C22.0008 7.60841 21.9756 7.47795 21.9258 7.35611C21.876 7.23427 21.8027 7.12346 21.71 7.03002L17.47 2.79002C17.3766 2.69734 17.2658 2.62401 17.1439 2.57425C17.0221 2.52448 16.8916 2.49926 16.76 2.50002C16.6284 2.49926 16.4979 2.52448 16.3761 2.57425C16.2543 2.62401 16.1435 2.69734 16.05 2.79002L13.22 5.62002L2.29002 16.55C2.19734 16.6435 2.12401 16.7543 2.07425 16.8761C2.02448 16.9979 1.99926 17.1284 2.00002 17.26V21.5C2.00002 21.7652 2.10537 22.0196 2.29291 22.2071C2.48045 22.3947 2.7348 22.5 3.00002 22.5H7.24002C7.37994 22.5076 7.51991 22.4857 7.65084 22.4358C7.78176 22.3858 7.90073 22.3089 8.00002 22.21L18.87 11.28L21.71 8.50002C21.8013 8.4031 21.8757 8.29155 21.93 8.17002C21.9397 8.09031 21.9397 8.00973 21.93 7.93002C21.9347 7.88347 21.9347 7.83657 21.93 7.79002L22 7.74002ZM6.83002 20.5H4.00002V17.67L13.93 7.74002L16.76 10.57L6.83002 20.5ZM18.17 9.16002L15.34 6.33002L16.76 4.92002L19.58 7.74002L18.17 9.16002Z" fill="black"/>
				</svg>
			</button>
		</div>
	</div>
</div>

<!-- Back button snippet -->
{#snippet backButton()}
	<button onclick={onBackClick} aria-label="Back">
		<svg xmlns="http://www.w3.org/2000/svg" width="32px" height="32px" viewBox="0 0 21 21">
			<g fill="none" fill-rule="evenodd" stroke="#000000" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round" transform="matrix(-1 0 0 1 18 3)">
				<path d="m10.595 10.5 2.905-3-2.905-3"/>
				<path d="m13.5 7.5h-9"/>
				<path d="m10.5.5-8 .00224609c-1.1043501.00087167-1.9994384.89621131-2 2.00056153v9.99438478c.0005616 1.1043502.8956499 1.9996898 2 2.0005615l8 .0022461"/>
			</g>
		</svg>					
	</button>
{/snippet}

<style>
	.question-header {
		background: var(--white, #ffffff);
		border-bottom: 2px solid var(--pitch-black, #000000);
		border-radius: 12px 12px 0 0;
		width: 100%;
		position: relative;
	}

	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 1rem;
		width: 100%;
	}

	.tools {
		display: flex;
		align-items: center;
		gap: 27px;
	}

	.tool-button {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 2px;
		padding: 0;
		background: none;
		border: none;
		cursor: pointer;
		color: var(--pitch-black, #000000);
		transition: opacity 0.2s ease;
		width: 24px;
		height: 24px;
	}

	.tool-button:hover {
		opacity: 0.7;
	}

	.tool-button:active {
		transform: translateY(1px);
	}

	.tool-button svg {
		width: 24px;
		height: 24px;
		flex-shrink: 0;
	}

	/* Responsive adjustments */
	@media (max-width: 1200px) {
		.header-content {
			padding: 0.5rem 1rem;
		}
	}

	@media (max-width: 768px) {
		.header-content {
			padding: 0.75rem 1rem;
		}
		
		.tools {
			gap: 1.25rem;
		}
	}

	@media (max-width: 540px) {
		.header-content {
			padding: 0.5rem 0.75rem;
		}
		
		.tools {
			gap: 1rem;
		}
		
		.tool-button svg {
			width: 1.25rem;
			height: 1.25rem;
		}
	}
</style>
